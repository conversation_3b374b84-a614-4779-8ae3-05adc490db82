let a = [1127584577, 121121759, 121125253, 121125848, 121192513, 121195561, 121443028, 121529630, 122123085, 122123666, 122127418, 122179839, 131346608, 131347069, 1504837401, 157449251, 158752055, 169801737, 170360252, 1779734032, 1935392108, 1987326534, 208513780, 208516916, 210014615, 214630948, 2396847377, 2574058534, 2661368410, 2661376995, 2662381758, 2750097524, 2752986405, 2846314478, 2896677838, 2957306939, 297432798, 3115675033, 3115684294, 3115686932, 3303371091, 3303376499, 469986973, 767999000, 769926798]

let b = [1127584577, 121121759, 121125253, 121125848, 121192513, 121195561, 121443028, 121529630, 122123085, 122123666, 122127418, 122179839, 131346608, 131347069, 157449251, 158752055, 169801737, 170360252, 1935392108, 1987326534, 208513780, 208516916, 210014615, 2396847377, 2574058534, 2661368410, 2661376995, 2662381758, 2750097524, 2752986405, 2846314478, 2896677838, 2957306939, 297432798, 3115675033, 3115684294, 3115686932, 3303352195, 3303371091, 3303376499, 767999000]


var minus = a.filter(function (v) {
    return b.indexOf(v) === -1;
});

console.log(minus)