# 八码数图求解器开发任务

## 任务描述
开发一个用于解决八码数图问题的Python程序，保存到 `puzzle-8.py` 文件中。

## 需求分析
- 八码数图是一个3x3的滑动拼图游戏
- 有8个数字块和1个空白位置（用*表示）
- 目标是将数字排列成标准顺序：123/456/78*
- 需要找到从初始状态到目标状态的最优解

## 技术方案
选择A*搜索算法，因为：
1. 能保证找到最优解（最少步数）
2. 使用曼哈顿距离作为启发式函数，搜索效率高
3. 相比BFS，内存使用更优化

## 实现要点

### 核心组件
1. **PuzzleState类**：表示拼图状态
   - 存储3x3棋盘状态
   - 计算曼哈顿距离启发式值
   - 生成邻居状态（上下左右移动）

2. **A*搜索算法**：
   - 使用优先队列管理待探索状态
   - f(n) = g(n) + h(n)，g为实际代价，h为启发式估计
   - 维护已访问状态集合避免重复

3. **可解性检查**：
   - 计算逆序对数量
   - 八码数图有解当且仅当逆序对数量为偶数

4. **路径重构**：
   - 从目标状态回溯到初始状态
   - 生成移动步骤序列

## 测试结果
使用示例输入测试：
```
初始状态：
378
416  
2*5

目标状态：
123
456
78*
```

**结果**：成功找到23步的最优解，程序运行正常。

## 文件结构
- `puzzle-8.py`：主程序文件
- `test_puzzle.py`：测试脚本
- `issues/八码数图求解器.md`：任务记录文档

## 程序特性
1. 交互式输入界面
2. 自动检查拼图可解性
3. 显示完整解决步骤
4. 演示每一步的移动过程
5. 错误处理和用户友好的提示

## 完成状态
✅ 任务已完成，程序功能正常，测试通过。
