// https://StoneStoryRPG.com/stonescript

/*Poison (∞) in the Caves of Fear and the Temple
Vigor  (♥) in the Mushroom Forest
Æther  (*) in the Haunted Halls
Fire   (φ) in the Bronze Mines
Ice    (❄) in the Icy Ridge
恐怖洞窟 & 神庙 中的 毒药(∞)
蘑菇森林 里的 活力(♥)
亡者之殿 中的 以太 (*)
灼热矿井 中的 火 (φ)
不融山中 的 冰 (❄)*/

//import Cosmetics/Hats/Skully
import Cosmetics/PetBoo
import Fishing
import UI/BetterInfo2

/** 预设变量 **/
?loc.begin
  var dpsChecker = 
  ^new StoneStoryRPG/scripts/dpsChecker
  var cdInfo = new StoneStoryRPG/scripts/cdInfo
  var util = new StoneStoryRPG/scripts/util

  var freeChestTracker =
  ^import UI/FreeChestTracker
  var myUtils = new UI/OkamiroyUtils

  var adaptiveDef

  var MAIN_LO = 2
  var HEAL_LO = 3
  var MAGIC_LO = 4

  var LOW_HP_PERSENT = 0.65
  var needHealToMaxHp = false

  var debug = false

  var ghostBossFlag = false
  var timer = 3*30
  var timer2 = 7*30

?loc.begin | loc.loop
  ghostBossFlag = false
  timer = 3*30
  timer2 = 7*30

  var aacTimer = 0
  var DEFAULT_FRAME = 15
  var aacFoeHP = 0
  var changeLR = false

debug = false

/** 装备针对当前敌人弱点的wand(魔杖) **/
func equipWeaknessWand()
  equipL wand @util.getWeakness()@ *10 +21
  equipR shield ah
/** 在循环后自动回满HP **/
func loopHealHP(healLO)
  ?hp <= maxhp * LOW_HP_PERSENT
    loadout @healLO@
  ?foe = boss
    needHealToMaxHp = true
  ?needHealToMaxHp = true &
  ^hp <= maxhp &
  ^foe ! boss &
  ^foe ! ice_wall
    loadout @healLO@
    ?hp = maxhp
      needHealToMaxHp = false

/** 显示自定义变量的值 **/
func showInfo()
  ?loc = undead_crypt_intro |
    ^ loc = uulaa_shop |
    ^ loc = waterfall |
    ^ loc = deadwood_river |
    ^ loc = cross_bridge |
    ^ loc = wildride_undead_crypt_gate |
    ^ loc = wildride_undead_crypt_halls |
    ^ loc.isQuest
    return false

  ?debug = true
    util.breakString(9,loc,loc.name)
    util.breakString(11,foe,foe.name)
    util.breakString(13,foe.buffs.string,＂buffs＂)
    // 显示当前装备的道具
    >c-42,9,#51D6FF,
    ^L:@item.left@\n
    ^R:@item.right@\n
    // 显示药水
    >c-42,11,
      ^@util.getColor(true)@,
      ^@item.potion@
  var itemL
  ?item.left
    itemL = string.Split(item.left,＂ ＂,true)［0］
  :
    itemL = ＂#＂
  var itemR
  ?item.right
    itemR = string.Split(item.right,＂ ＂,true)［0］
  :
    itemR = ＂#＂
  var itemSpace = ＂＂
  var itemSpaceNum = 15 - string.Size(itemL) - string.Size(itemR)
  ?itemSpaceNum > 0
    for i = 1..itemSpaceNum
      itemSpace = itemSpace + ＂#＂
  // 显示当前装备的道具
  >c-42,10,#51D6FF,
  ^L:@itemL@@itemSpace@R:@itemR@\n

  // 显示药水
  ?item.potion = empty
    >c-42,11,
      ^@util.getColor(false)@,
      ^空瓶子
  :
    var potionNameAtIP = 
      ^string.IndexOf(item.potion, ＂药水＂) - 2
    var potionName = string.Sub(
      ^item.potion, potionNameAtIP, 4
    ^)
    >c-42,11,
      ^@util.getColor(true)@,
      ^@potionName@

  // 显示是否需要回满血
  >c-37,11,
    ^@util.getColor(needHealToMaxHp)@,
    ^NeedHealToMaxHp
  // 显示低HP血量
  >c-21,11,
    ^@util.getColor(hp <= maxhp * LOW_HP_PERSENT)@,
    ^@maxhp * LOW_HP_PERSENT@

  cdInfo.showCDWindow(screen.w - 10,6)
  myUtils.ShowTimeStats(1, 2)
  myUtils.ShowTimer(14, 15)
  dpsChecker.dpsChecker(1,15)
  myUtils.ShowFoesBar(4, 1, 30, false)
  myUtils.ShowPlayersBar(3, -5, 7)
/** 自动取消后摇 **/
func AAC1H(iL,iR)
  equipL @iL@
  equipR @iR@
  ?ai.idle
    equip skeleton
    equipL @iL@
    equipR @iR@
/** 自动取消后摇 **/
func AAC2H(item2H)
  equip @item2H@
  ?ai.idle
    equip shield
    equip @item2H@
/** 使用指定盾牌 **/
func useShield(shield)
  // ?debug = true
  >`0,10,@shield@ @item.GetCooldown(shield)@ @item.CanActivate(shield)@
  ?item.GetCooldown(shield) <= 0
    equipL sword Vigor
    equipR @shield@
    return true
  :
    return false
/** 使用骷髅手臂
 *  在骷髅手臂可用,
 *  可以使用技能秒杀怪物
 *  非物理免疫,非boss
 *  不会干扰加速跑和自动捡取时
 *  装备并且使用骷髅手臂
**/
func steal()
  ?(foe=boss & loc=deadwood & foe=phase2) |
  ^loc = undead_crypt_boss_harder |
  ^loc = bronze_guardian_harder
    return false

  ?item.GetCooldown(＂skeleton_arm＂) <= 0 &
  ^(foe.hp + foe.armor) < 44 &
  ^foe ! immune_to_physical &
  ^pickup.distance >= 20 &
  ^harvest.distance >= 20
    equip skeleton arm
    ?item.CanActivate(＂skeleton_arm＂)
       activate R
       >o-2,-3,#ffcccc,shhhh!~
/** 点击重置按钮之后重置
 *  blade_used_monsters
**/
func OnRstBtnPressed()
  var monsArr = string.Split(
                  ^storage.Get(
                    ^＂blade_used_monsters＂
                  ^)
                ^)
  monsArr.RemoveAt(monsArr.IndexOf(＂青铜守卫＂))
  monsArr.RemoveAt(monsArr.IndexOf(＂火元素＂))

  storage.Set(
    ^＂blade_used_monsters＂,
    ^string.Join(＂ ＂, monsArr)
  ^)
  //storage.Delete(＂blade_used_monsters＂)

/** 添加重置按钮 **/
func addRstBtn()
  ?loc.begin
    var rstBtn = ui.AddButton()
    rstBtn.y = 1
    rstBtn.text = 删除指定元素
    rstBtn.SetPressed(OnRstBtnPressed)
/** 使用堕神大剑的主动能力 **/
func useBlade()
  ?(foe=boss & loc=deadwood & foe=phase2) |
  ^loc = undead_crypt_boss_harder
    return false

  ?item.GetCooldown(＂blade＂) <= 0 &
  ^foe.count > 5 &
  ^foe.distance <= 20 &
  ^pickup.distance >= 20 &
  ^harvest.distance >= 20
    equip blade
    ?item.CanActivate(＂blade＂)
      activate R
      >o-2,-3,#ffcccc,使用剑
      return true
  return false

/** 自动统计被堕神之剑的主动能力杀死的敌人
 *  用于完成 击败35类敌人 的任务
 *  由于无法获取全部敌人信息
 *  只能多次执行
**/
func bladeMission35kill()
  var monsArr
  var foeName
  monsArr = ［］
  foeName = foe.name

  ?foe & foe.distance <= 20
    ?storage.Has(＂blade_used_monsters＂)
      monsArr =
      ^string.Split(
        ^storage.Get(＂blade_used_monsters＂),
        ^true
      ^)
      ? ! monsArr.Contains(foeName)
        ?useBlade() = true
          monsArr.Add(foeName)
    :
      ?useBlade() = true
        monsArr.Add(foeName)
    ?monsArr ! ［］
      storage.Set(
        ^＂blade_used_monsters＂,
        ^string.Join(＂ ＂, monsArr)
      ^)

  util.breakString(10,foe.name,@nowMon)
  util.breakString(
    ^11,
    ^storage.Get(＂blade_used_monsters＂),
    ^＂monsNum＂ + string.Split(
      ^storage.Get(＂blade_used_monsters＂)
    ^)
    ^.Count() + ＂/35＂
  ^)
/** 完成400000重击伤害任务 **/
func debuffAtk()
  ? (!(foe &
  ^pickup.distance >= 20 &
  ^harvest.distance >= 20)) |
  ^ hp <= maxhp * LOW_HP_PERSENT |
  ^ foe = immune_to_ranged |
  ^ string.IndexOf(
    ^foe.buffs.string,
    ^＂buff_protection＂
  ^) ! -1
    return false

  var debuffStr = foe.debuffs.string
  ?string.IndexOf(
    ^debuffStr,＂debuff_damage:1＂
  ^) = -1&
    ^ foe ! immune_to_debuff_damage
    >`1,14,#C94277,debuff:poison
    equipL wand poison
    equipR shield ah
  :?string.IndexOf(
    ^debuffStr,＂debuff_chill:6＂
  ^) = -1&
    ^ foe ! immune_to_debuff_chill
    >`1,14,#C94277,debuff:ice
    equipL wand ice
    equipR shield ah
  :?string.IndexOf(
    ^debuffStr,＂debuff_dot:1＂
  ^) = -1&
    ^ foe ! immune_to_debuff_dot
    >`1,14,#C94277,debuff:fire
    equipL wand fire
    equipR shield ah
  :?string.IndexOf(
    ^debuffStr,＂stun:1＂
  ^) = -1 &
    ^ foe ! immune_to_stun &
    ^ foe ! immune_to_physical &
    ^ (loc ! mine & foe ! explode)
    >`1,14,#C94277,debuff:stun
    equipL hammer *10 +21
    equipR shield ah
/** 使用面具的主动能力 **/
func useMask()
  ?(foe = boss & loc = temple)
    return false

  ?item.GetCooldown(＂mask＂) <= 0 &
  ^foe.count > 0 &
  ^foe.distance <= 20 &
  ^pickup.distance >= 20 &
  ^harvest.distance >= 20
    equip mask
    ?item.CanActivate(＂mask＂)
      activate R
      >o-2,-3,#ffcccc,使用面具
      return true
  return false
?loc.begin | loc.loop
  var hammerTimer = 0
  var hammerFlag = false
func useHeavyHammer()
  ?foe.armor > 0 & 
  ^foe = boss
    ?debug = true
      >`0,10,hammerTimer:@hammerTimer@\n
      ^hammerFlag:@hammerFlag@\n
      ^distance:@foe.distance@\n
      ^GetCooldown:@item.GetCooldown(＂hammer＂)@\n
      ^CanActivate:@item.CanActivate(＂hammer＂)@
    equip heavy hammer
    ?foe.distance <= 8
      ?item.GetCooldown(＂hammer＂) <= 0 | hammerFlag
        ?item.CanActivate(＂hammer＂)
          hammerFlag = true
    ?hammerTimer < 30 &
    ^hammerFlag = true
      hammerTimer++
    :
      hammerTimer = 0
      hammerFlag = false
    ?hammerTimer >= 10
      >`0,2,activateR
      activate R
/** 自动拾取
 *  距离可拾取物品足够近的时候
 *  自动拾取物品
**/
func autoPickup()
  ?pickup & pickup.distance < 10
    equipL star_stone
    equipR shield ah
/** 血量小于7喝药 **/
func autoPotion()
  ?item.potion = empty
    return false
  var potionNameAtIP = 
    ^string.IndexOf(item.potion, ＂药水＂) - 2
  var potionName = string.Sub(
    ^item.potion, potionNameAtIP, 4
  ^)
  ?hp <= maxhp*0.3 &
  ^potionName = ＂恢复药水＂
    activate potion
/** 加速
 *  在离怪物,可拾取物,可收获物
 *  足够远的时候装备三曲腿加速跑
**/
func runFast()
  ?foe.distance > 20 &
  ^ pickup.distance >= 20 &
  ^ harvest.distance >= 20
    equipL triskelion stone
    equipR shield ah
func brewPotion()
  /*
  stone 石头, wood 木头,bronze 青铜,tar 焦油
  恢复药水 (生命+=最大生命值)                      油
  防御药水 (生命+=0.5*最大生命值|护甲+=最大生命值)  石+油
  净化药水 (生命+=0.5*最大生命值|-全部负面效果)     木+油
  吸血药水 (20s|生命+=伤害值*0.2)                 铜+油
  怪力药水 (10s|攻击眩晕|护甲伤害x3)               石
  隐形药水 (15s|100% 躲避)                        石+木
  幸运药水 (6s|100% 暴击)                         石+铜
  狂暴药水 (10s|+15 攻速)                         木+铜
  闪电药水 (所有敌人150伤害)                       铜
  经验药水 (30s|1经验&amp;钱/击杀)                 木
  */
  ?loc.begin
    ?loc = mine
      brew stone
    :
      brew tar
/** 岩石高地
 *  自动铲石头
 *  useShovel: 是否采集石头
**/
func levelRocky(useShovel)
  ?loc ! rocky
    return false
  AAC1H(
    ^＂socketed_sword ＂+util.getWeakness()+＂ D *10 +21＂,
    ^＂socketed_sword ＂+util.getWeakness()+＂ D *10 +20＂
  ^)
  // // 自动铲石头
  ?harvest = Boulder &
  ^harvest.distance < 30 &
  ^useShovel
    equip shovel
    return true
  
  ?foe.buffs.string
    adaptiveDef = foe.buffs.string
    adaptiveDef = string.Split(
                    ^adaptiveDef,
                    ^＂:＂,
                    ^true
                  ^)［1］
    adaptiveDef = string.Split(
                    ^adaptiveDef,
                    ^＂adaptive_defense_＂,
                    ^true
                  ^)［0］
  :
    adaptiveDef = ＂＂

  ?adaptiveDef = Poison
    AAC1H(
      ^＂socketed_sword Vigor D *10 +21＂,
      ^＂socketed_sword Vigor D *10 +20＂
    ^)
  :?adaptiveDef = Vigor
    AAC1H(
      ^＂socketed_sword AEther D *10 +21＂,
      ^＂socketed_sword AEther D *10 +20＂
    ^)
  :?adaptiveDef = AEther
    AAC1H(
      ^＂socketed_sword Fire D *10 +21＂,
      ^＂socketed_sword Fire D *10 +20＂
    ^)
  :?adaptiveDef = Fire
    AAC1H(
      ^＂socketed_sword Ice D *10 +21＂,
      ^＂socketed_sword Ice D *10 +20＂
    ^)
  :?adaptiveDef = Ice
    AAC1H(
      ^＂socketed_sword Poison D *10 +21＂,
      ^＂socketed_sword Poison D *10 +20＂
    ^)
  ?util.getWeakness() = false
    AAC1H(
      ^＂socketed_sword Vigor dL *10 +21＂,
      ^＂socketed_sword Vigor dL *10 +20＂
    ^)
  debuffAtk()
  >`0,10,<EMAIL>@ <EMAIL>@
  ?foe.armor > 0
    AAC1H(
      ^＂war hammer AEther D  *10 +21＂,
      ^＂war hammer AEther aU *10 +20＂
    ^)
    ?foe.state = 107
      equipL wand ice *10 +21
      equipR wand Vigor *10 +21
  ?hp <= maxhp * LOW_HP_PERSENT
    AAC1H(
      ^＂socketed_sword Vigor dL *10 +21＂,
      ^＂socketed_sword Vigor dL *10 +20＂
    ^)
  ?foe.state = 115 & foe.time >= 60
    equipL mind stone
/** 枯木峡谷
*  自动砍树
*  useHatchet: 是否砍树
*  healLO: 治疗Loadout
**/
func levelDeadwood(useHatchet, healLO)
  ?loc ! deadwood
    return false
  AAC1H(
    ^＂socketed_sword ＂+util.getWeakness()+＂ D *10 +21＂,
    ^＂socketed_sword ＂+util.getWeakness()+＂ D *10 +20＂
  ^)
  debuffAtk()
  loopHealHP(healLO)
  // 魔免怪加载近战
  ?foe = immune_to_ranged
    AAC1H(
      ^＂socketed_sword Vigor dL *10 +21＂,
      ^＂socketed_sword Vigor dL *10 +20＂
    ^)
  // // 自动砍树
  ?harvest = Tree & useHatchet
    equip hatchet
  ?foe = wasp
    equipL wand ice
    equipR shield ah
  ?foe = boss & foe = phase2
    equipL war hammer *10 +21
    equipR socketed_sword Vigor *10 +21
/** 恐怖洞窟
*  mainLO: 主Loadout
*  healLO: 治疗Loadout
**/
func levelCaves(mainLO,healLO)
  ?loc ! caves
    return false
  loadout @mainLO@
  debuffAtk()
  loopHealHP(healLO)
  ?foe = tiny_spider
    equipWeaknessWand()
/** 蘑菇森林
*  mainLO: 主Loadout
*  healLO: 治疗Loadout
**/
func levelForest(mainLO,healLO)
  ?loc ! forest
    return false
  loadout @mainLO@
  debuffAtk()
  loopHealHP(healLO)
  ?foe = ant
    equipWeaknessWand()
/** 亡者之殿
*  mainLO: 主Loadout
*  healLO: 治疗Loadout
*  magicLO: 魔法伤害Loadout
*  ? *>15 : use crossbow 3sec.
**/
func levelHalls(mainLO,healLO)
  ?loc ! halls
    return false
  loadout @mainLO@
  debuffAtk()
  loopHealHP(healLO)
  ?foe = immune_to_physical
    equipL wand Vigor
    equipR shield ah
  /*
  如果是召唤boss,用3秒的冰驽

  如果还在打large_ghost,等7s,
  如果还是large_ghost,再来3s
  */
  ?foe = ghost_tomb &
  ^foe = boss &
  ^foe = spawner
    timer = 3*30
    ghostBossFlag = true

  ?timer > 0 & ghostBossFlag = true
    equip crossbow ice D *10 +21
    timer--
  :
    timer = 3*30
    ghostBossFlag = false
    ?foe = large_ghost
      ?timer2 > 0 & ghostBossFlag = false
        timer2--
      :
        timer2 = 7*30
        ?foe = large_ghost
          ghostBossFlag = true
  ?debug = true
    >`1,8,
    ^@util.getColor(ghostBossFlag)@,
    ^timer:@timer@,
    ^timer2:@timer2@

  ?loc = undead_crypt_boss_harder
    equipL wand Vigor
    equipR socketed_sword Vigor *10 +21

/** 灼热矿井
*  healLO: 治疗Loadout
**/
func levelMine(healLO)
  ?loc ! mine
    return false
  ?loc.loop
    needHealToMaxHp = false
  equipWeaknessWand()
  debuffAtk()
  ?foe = boss
    ?foe = guardian
      ?item.potion ! empty
        var potionNameAtIP = 
          ^string.IndexOf(item.potion, ＂药水＂) - 2
        var potionName = string.Sub(
          ^item.potion, potionNameAtIP, 4
        ^)
        ?potionName = ＂怪力药水＂
        activate potion
      equip Crossbow AEther D *10 +21
    //贴身攻击
    ?foe.state = 33 &
    ^foe.time <= 180
      ?item.GetCooldown(＂hammer＂) <= 0 | hammerFlag = true
        useHeavyHammer()
      :
        ?foe.armor > 0
          AAC1H(
            ^＂war hammer AEther D  *10 +21＂,
            ^＂war hammer AEther aU *10 +20＂
          ^)
        :
          AAC1H(
            ^＂socketed_sword ＂+util.getWeakness()+＂ D *10 +21＂,
            ^＂socketed_sword ＂+util.getWeakness()+＂ D *10 +20＂
          ^)
      ?foe.distance >= 10
        var dashUsed = useShield(dash)
        ?dashUsed = false
          useShield(bash)
    :
      debuffAtk()
    // 躲锤子
    ?foe.state = 32
      ?foe.time >= 32
        equipL mind stone
    // 自爆
    ?foe = explode
      equipL wand Ice *10 +21
      equipR wand @util.getWeakness()@ *10 +21
  loopHealHP(healLO)
  
  ?(foe = fluff | foe = explode) &
  ^foe.distance <= 5
    equipL mind stone
/** 不融山
*  healLO: 治疗Loadout
**/
func levelIcy(healLO)
  ?loc ! icy
    return false
  AAC1H(
    ^＂socketed_long_sword Fire D *10 +21＂,
    ^＂socketed_long_sword Fire D *10 +20＂
  ^)
  ?foe ! ice_wall
    ?foe ! boss &
    ^foe.distance >= 10
      var dashUsed = useShield(dash)
      ?dashUsed = false
        useShield(bash)
    debuffAtk()
  ?foe ! ice_pillar
    loopHealHP(healLO)
/** 神庙
*  healLO: 治疗Loadout
*  magicLO: 魔法伤害Loadout
**/
func levelTemple(healLO,magicLO)
  ?loc ! temple
    return false
  AAC1H(
    ^＂socketed_sword ＂+util.getWeakness()+＂ D *10 +21＂,
    ^＂socketed_sword ＂+util.getWeakness()+＂ D *10 +20＂
  ^)
  debuffAtk()
  loopHealHP(healLO)
  // 魔免怪加载近战
  ?foe = immune_to_Vigor
    loadout @magicLO@
  ?foe = flying_serpent
    equipWeaknessWand()
/** 显示宝箱位置 **/
func freeChest()
  ?loc = undead_crypt_intro
    freeChestTracker.Main()
/** 帕拉斯大庆典 **/
func wildRide()
  ?loc = ＂wildride_undead_crypt_gate＂
    >o0,-2,hint: 一朝战败,万念俱灰
  ?loc = ＂wildride_undead_crypt_halls＂
    ?pos.x = 120
      >o0,-3,hint: 悄悄话,200多个
    :?pos.x = 255
      >o0,-2,hint: 打断 ,布洛希
// 酿造药水
brewPotion()
/** 执行对应关卡的策略 **/
levelRocky(true)
levelDeadwood(false, HEAL_LO)
levelCaves(MAIN_LO,HEAL_LO)
levelForest(MAIN_LO,HEAL_LO)
levelHalls(MAIN_LO,HEAL_LO)
levelMine(HEAL_LO)
levelIcy(HEAL_LO)
levelTemple(HEAL_LO,MAGIC_LO)
// 宝箱小游戏提示
freeChest()
wildRide()
/** 其他功能 **/
// 自动拾取
autoPickup()

//bladeMission35kill()
//addRstBtn()

// 偷取道具
steal()
// 堕神大剑相关
useBlade()

// 使用面具
useMask()
// 低血量回血
autoPotion()
// 加速跑
runFast()
// 显示对应的提示信息
showInfo()
/** 完毕 **/
