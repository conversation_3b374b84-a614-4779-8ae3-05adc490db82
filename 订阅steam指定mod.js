// 在steam合集页面执行 https://steamcommunity.com/sharedfiles/filedetails/?id=3298909783
let idList = [909981683, 940974914, 279856823, 279853535, 626187474, 483874814, 2165823958, 262355822, 2586758926, 627562239, 912257624, 638169501, 2906960647, 2108624514, 3293883037, 3264480851, 3254801087, 2598086924, 2687046933, 2632094627, 2940602228, 3170881809, 2348791065, 250343117, 646243097, 2476804138, 2018041301, 3275799192, 540427507, 661916638, 3280184831, 3271293055, 1416400653, 1870633213, 238552954, 663451679, 1864838431, 1864794848, 1312354014, 127164671, 422026398, 3156946801, 2680156276, 775711549, 754994495, 1380516683, 232980585, 249601409, 242266504, 234179589, 127198293, 251887563, 645910193, 974949258, 243776670, 242814827, 243513783, 243100406, 242405966, 2588751433, 666010236, 656416130, 373048945, 647383519, 127192234, 241406979, 481128782, 503781672, 481754016, 127167490, 954000760, 788564919, 127193408, 122945959, 241090600, 180925247, 3253484151, 934602353, 383457943, 281291625, 121144623, 2778742681, 3035669635, 769926798, 214630948, 2396847377, 2662381758, 2661376995, 2661368410, 2846314478, 1987326534, 2574058534, 131347069, 131346608, 158752055, 157449251, 210014615, 2750097524, 2957306939, 122123666, 122123085, 122179839, 121529630, 121125848, 121125253, 121121759, 3115686932, 3115684294, 3115675033, 121443028, 2752986405, 128426961, 128425775, 128424524, 469986973, 297432798, 2896677838, 122127418, 1127584577, 169801737, 170360252, 767999000]

idList.forEach((e, i) => {
    setTimeout(() => {
        console.log('choice_MySubscribedItems_' + e, '' + e)
        ToggleChildInCollection('choice_MySubscribedItems_' + e, '' + e)
    }, 1000 * i)
})