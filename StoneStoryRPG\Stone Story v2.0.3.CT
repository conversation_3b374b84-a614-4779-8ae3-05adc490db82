<?xml version="1.0" encoding="utf-8"?>
<CheatTable CheatEngineTableVersion="29">
  <CheatEntries>
    <CheatEntry>
      <ID>1</ID>
      <Description>"Potion Info (Use/Create Potion To Update)"</Description>
      <Options moHideChildren="1"/>
      <LastState/>
      <VariableType>Auto Assembler Script</VariableType>
      <AssemblerScript>{$Lua}
LaunchMonoDataCollector()
{$Asm}

define(address,Potion:UpdateContent+b)
define(bytes,48 63 86 C8 00 00 00)

[ENABLE]
assert(address,bytes)
alloc(newmem,$128,address)

label(return)
label(potion_bp)
registersymbol(potion_bp)

newmem:
  mov [potion_bp],rsi //get potion base pointer
  movsxd  rax,dword ptr [rsi+000000C8]
  jmp return

potion_bp:
  dq 0

address:
  jmp newmem
  nop
  nop
return:

[DISABLE]
address:
  db bytes

unregistersymbol(potion_bp)
dealloc(newmem)
</AssemblerScript>
      <CheatEntries>
        <CheatEntry>
          <ID>0</ID>
          <Description>"Type"</Description>
          <DropDownList DescriptionOnly="1" DisplayValueAsItem="1">00000000:Empty
00000001:Healing (hp+=mhp)
00000002:Defensive (hp+=0.5*mhp|arm+=mhp)
00000003:Cleansing (hp+=0.5*mhp|-debuffs)
00000004:vampiric (20s|hp+=dmg*0.2)
00000005:Strength (10s|atk stun|arm dmgx3)
00000006:Invisibility (15s|100% eva)
00000007:Lucky (6s|100% crit chance)
00000009:Berserk (10s|+15 atk spd)
0000000A:Lightning (150 dmg all)
0000000B:Experience (30s|1xp&amp;ki/kill)
</DropDownList>
          <LastState Value="0000000B" RealAddress="07857A68"/>
          <ShowAsHex>1</ShowAsHex>
          <VariableType>4 Bytes</VariableType>
          <Address>[potion_bp]+C8</Address>
          <Hotkeys>
            <Hotkey>
              <Action>Set Value</Action>
              <Keys>
                <Key>65</Key>
              </Keys>
              <Value>7</Value>
              <Description>Lucky (6s|100% crit chance)</Description>
              <ID>1</ID>
            </Hotkey>
            <Hotkey>
              <Action>Set Value</Action>
              <Keys>
                <Key>83</Key>
              </Keys>
              <Value>9</Value>
              <Description>Berserk (10s|+15 atk spd)</Description>
              <ID>2</ID>
            </Hotkey>
            <Hotkey>
              <Action>Set Value</Action>
              <Keys>
                <Key>68</Key>
              </Keys>
              <Value>5</Value>
              <Description>Strength (10s|atk stun|arm dmgx3)</Description>
              <ID>3</ID>
            </Hotkey>
            <Hotkey>
              <Action>Set Value</Action>
              <Keys>
                <Key>90</Key>
              </Keys>
              <Value>6</Value>
              <Description>Invisibility (15s|100% eva)</Description>
              <ID>0</ID>
            </Hotkey>
            <Hotkey>
              <Action>Set Value</Action>
              <Keys>
                <Key>88</Key>
              </Keys>
              <Value>B</Value>
              <Description>Experience (30s|1xp&amp;ki/kill)</Description>
              <ID>4</ID>
            </Hotkey>
            <Hotkey>
              <Action>Set Value</Action>
              <Keys>
                <Key>67</Key>
              </Keys>
              <Value>1</Value>
              <Description>Healing (hp+=mhp)</Description>
              <ID>5</ID>
            </Hotkey>
            <Hotkey>
              <Action>Set Value</Action>
              <Keys>
                <Key>87</Key>
              </Keys>
              <Value>A</Value>
              <Description>Lightning (150 dmg all)</Description>
              <ID>6</ID>
            </Hotkey>
          </Hotkeys>
        </CheatEntry>
      </CheatEntries>
    </CheatEntry>
    <CheatEntry>
      <ID>8</ID>
      <Description>"Buffs Last Infinitely And Debuffs Expire Instantly"</Description>
      <LastState/>
      <VariableType>Auto Assembler Script</VariableType>
      <AssemblerScript>{$Lua}
LaunchMonoDataCollector()
{$Asm}

define(address,StatModifier:UpdateTic+1f)
define(bytes,48 63 46 6C FF C0)

[ENABLE]
assert(address,bytes)
alloc(newmem,$128,address)

label(elapsedTicks)
label(return)

newmem:
  movsxd rax,dword ptr [rsi+60] //get effect duration
  cmp byte ptr [rsi+65],1       //buff or debuff?
  jne elapsedTicks              //
  xor eax,eax                   //if buff then reset elapsed time
elapsedTicks:
  mov dword ptr [rsi+6C],rax
  inc eax
  jmp return

address:
  jmp newmem
  nop
return:

[DISABLE]
address:
  db bytes

dealloc(newmem)
</AssemblerScript>
    </CheatEntry>
    <CheatEntry>
      <ID>13</ID>
      <Description>"Inf. Resources (Get/Spend To Update)"</Description>
      <LastState/>
      <VariableType>Auto Assembler Script</VariableType>
      <AssemblerScript>{$Lua}
LaunchMonoDataCollector()
{$Asm}

define(address,InventoryResources:GetCipheredResource+4e)
define(bytes,48 8B 4D F0 48 2B C1)

[ENABLE]
assert(address,bytes)
alloc(newmem,$128,address)

label(return)

newmem:
  mov rcx,[rbp-10]
  mov rax,#900000 //substitute unciphered value with user input
  jmp return

address:
  jmp newmem
  nop
  nop
return:

[DISABLE]
address:
  db bytes

dealloc(newmem)
</AssemblerScript>
    </CheatEntry>
    <CheatEntry>
      <ID>17</ID>
      <Description>"Anvil Info"</Description>
      <Options moHideChildren="1"/>
      <LastState/>
      <VariableType>Auto Assembler Script</VariableType>
      <AssemblerScript>{$Lua}
LaunchMonoDataCollector()
{$Asm}

define(address,AnvilScreen:Update+f)
define(bytes,48 8D AD 00 00 00 00)

[ENABLE]
assert(address,bytes)
alloc(newmem,$128,address)

label(return)
label(anvil_bp)
registersymbol(anvil_bp)

newmem:
  mov [anvil_bp],rsi //get anvil base pointer
  lea rbp,[rbp+00000000]
  jmp return

anvil_bp:
  dq 0

address:
  jmp newmem
  nop
  nop
return:

[DISABLE]
address:
  db bytes

unregistersymbol(anvil_bp)
dealloc(newmem)

</AssemblerScript>
      <CheatEntries>
        <CheatEntry>
          <ID>18</ID>
          <Description>"Second Slot Count"</Description>
          <LastState Value="1024" RealAddress="05413554"/>
          <VariableType>4 Bytes</VariableType>
          <Address>[anvil_bp]+134</Address>
        </CheatEntry>
        <CheatEntry>
          <ID>14</ID>
          <Description>"Anvil Second Slot Max Count"</Description>
          <Options moHideChildren="1"/>
          <LastState/>
          <VariableType>Auto Assembler Script</VariableType>
          <AssemblerScript>{$Lua}
LaunchMonoDataCollector()
{$Asm}

define(address,AnvilScreen:GetSecondSlotCountLimit+15b)
define(bytes,48 8D 65 00 5D)

[ENABLE]
assert(address,bytes)
alloc(newmem,$128,address)

label(return)
label(Max_Count)
registersymbol(Max_Count)

newmem:
  mov eax,[Max_Count] //substitute final result with user input
  lea rsp,[rbp+00]
  pop rbp
  jmp return

Max_Count:
  dd #1024

address:
  jmp newmem
return:

[DISABLE]
address:
  db bytes

unregistersymbol(Max_Count)
dealloc(newmem)
</AssemblerScript>
          <CheatEntries>
            <CheatEntry>
              <ID>19</ID>
              <Description>"2^&lt;desired level&gt;=&lt;item amount&gt;, Max Level is 10"</Description>
              <LastState Value="" RealAddress="00000000"/>
              <GroupHeader>1</GroupHeader>
            </CheatEntry>
            <CheatEntry>
              <ID>15</ID>
              <Description>"Value"</Description>
              <VariableType>4 Bytes</VariableType>
              <Address>Max_Count</Address>
            </CheatEntry>
          </CheatEntries>
        </CheatEntry>
      </CheatEntries>
    </CheatEntry>
  </CheatEntries>
  <CheatCodes>
    <CodeEntry>
      <Description>Change of jnl AnvilScreen:HandlePlusPressed+149</Description>
      <AddressString>075A7B18</AddressString>
      <Before>
        <Byte>00</Byte>
        <Byte>00</Byte>
        <Byte>41</Byte>
        <Byte>3B</Byte>
        <Byte>C6</Byte>
      </Before>
      <Actual>
        <Byte>0F</Byte>
        <Byte>8D</Byte>
        <Byte>0B</Byte>
        <Byte>01</Byte>
        <Byte>00</Byte>
        <Byte>00</Byte>
      </Actual>
      <After>
        <Byte>41</Byte>
        <Byte>BF</Byte>
        <Byte>01</Byte>
        <Byte>00</Byte>
        <Byte>00</Byte>
      </After>
    </CodeEntry>
    <CodeEntry>
      <Description>Change of jne AnvilScreen:HandlePlusPressed+f3</Description>
      <AddressString>075A7BAC</AddressString>
      <Before>
        <Byte>8B</Byte>
        <Byte>45</Byte>
        <Byte>D0</Byte>
        <Byte>3B</Byte>
        <Byte>C1</Byte>
      </Before>
      <Actual>
        <Byte>75</Byte>
        <Byte>25</Byte>
      </Actual>
      <After>
        <Byte>48</Byte>
        <Byte>8B</Byte>
        <Byte>87</Byte>
        <Byte>90</Byte>
        <Byte>00</Byte>
      </After>
    </CodeEntry>
  </CheatCodes>
  <UserdefinedSymbols/>
</CheatTable>
