{"runtimeTarget": {"name": ".NETCoreApp,Version=v6.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v6.0": {"UABEAvalonia/1.0.0": {"dependencies": {"AssetRipper.TextureDecoder": "1.2.0", "Avalonia": "11.0.1", "Avalonia.AvaloniaEdit": "11.0.1", "Avalonia.Controls.DataGrid": "11.0.1", "Avalonia.Desktop": "11.0.1", "Avalonia.Diagnostics": "11.0.1", "Avalonia.Themes.Fluent": "11.0.1", "Avalonia.Themes.Simple": "11.0.1", "AvaloniaEdit.TextMate": "11.0.1", "Mono.Cecil": "0.11.4", "Newtonsoft.Json": "13.0.3-beta1", "Samboy063.LibCpp2IL": "2022.0.7.2", "SixLabors.ImageSharp": "3.1.4", "AssetsTools.NET.Cpp2IL": "*******", "AssetsTools.NET": "*******", "AssetsTools.NET.MonoCecil": "*******", "AssetsTools.NET.Texture": "*******"}, "runtime": {"UABEAvalonia.dll": {}}}, "AssetRipper.TextureDecoder/1.2.0": {"runtime": {"lib/net6.0/AssetRipper.TextureDecoder.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Avalonia/11.0.1": {"dependencies": {"Avalonia.BuildServices": "0.0.28", "Avalonia.Remote.Protocol": "11.0.1", "MicroCom.Runtime": "0.11.0", "System.ComponentModel.Annotations": "4.5.0"}, "runtime": {"lib/net6.0/Avalonia.Base.dll": {"assemblyVersion": "********", "fileVersion": "********"}, "lib/net6.0/Avalonia.Controls.dll": {"assemblyVersion": "********", "fileVersion": "********"}, "lib/net6.0/Avalonia.DesignerSupport.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}, "lib/net6.0/Avalonia.Dialogs.dll": {"assemblyVersion": "********", "fileVersion": "********"}, "lib/net6.0/Avalonia.Markup.Xaml.dll": {"assemblyVersion": "********", "fileVersion": "********"}, "lib/net6.0/Avalonia.Markup.dll": {"assemblyVersion": "********", "fileVersion": "********"}, "lib/net6.0/Avalonia.Metal.dll": {"assemblyVersion": "********", "fileVersion": "********"}, "lib/net6.0/Avalonia.MicroCom.dll": {"assemblyVersion": "********", "fileVersion": "********"}, "lib/net6.0/Avalonia.OpenGL.dll": {"assemblyVersion": "********", "fileVersion": "********"}, "lib/net6.0/Avalonia.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Avalonia.Angle.Windows.Natives/2.1.0.2023020321": {"runtimeTargets": {"runtimes/win-arm64/native/av_libglesv2.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "2.1.0.0"}, "runtimes/win-x64/native/av_libglesv2.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "2.1.0.0"}, "runtimes/win-x86/native/av_libglesv2.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "2.1.0.0"}}}, "Avalonia.AvaloniaEdit/11.0.1": {"dependencies": {"Avalonia": "11.0.1", "System.Collections.Immutable": "5.0.0"}, "runtime": {"lib/netstandard2.0/AvaloniaEdit.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Avalonia.BuildServices/0.0.28": {}, "Avalonia.Controls.ColorPicker/11.0.1": {"dependencies": {"Avalonia": "11.0.1", "Avalonia.Remote.Protocol": "11.0.1"}, "runtime": {"lib/net6.0/Avalonia.Controls.ColorPicker.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Avalonia.Controls.DataGrid/11.0.1": {"dependencies": {"Avalonia": "11.0.1", "Avalonia.Remote.Protocol": "11.0.1"}, "runtime": {"lib/net6.0/Avalonia.Controls.DataGrid.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Avalonia.Desktop/11.0.1": {"dependencies": {"Avalonia": "11.0.1", "Avalonia.Native": "11.0.1", "Avalonia.Skia": "11.0.1", "Avalonia.Win32": "11.0.1", "Avalonia.X11": "11.0.1"}, "runtime": {"lib/net6.0/Avalonia.Desktop.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Avalonia.Diagnostics/11.0.1": {"dependencies": {"Avalonia": "11.0.1", "Avalonia.Controls.ColorPicker": "11.0.1", "Avalonia.Controls.DataGrid": "11.0.1", "Avalonia.Themes.Simple": "11.0.1", "Microsoft.CodeAnalysis.CSharp.Scripting": "3.8.0", "Microsoft.CodeAnalysis.Common": "3.8.0"}, "runtime": {"lib/net6.0/Avalonia.Diagnostics.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Avalonia.FreeDesktop/11.0.1": {"dependencies": {"Avalonia": "11.0.1", "Tmds.DBus.Protocol": "0.15.0"}, "runtime": {"lib/net6.0/Avalonia.FreeDesktop.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Avalonia.Native/11.0.1": {"dependencies": {"Avalonia": "11.0.1"}, "runtime": {"lib/net6.0/Avalonia.Native.dll": {"assemblyVersion": "********", "fileVersion": "********"}}, "runtimeTargets": {"runtimes/osx/native/libAvaloniaNative.dylib": {"rid": "osx", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "Avalonia.Remote.Protocol/11.0.1": {"runtime": {"lib/net6.0/Avalonia.Remote.Protocol.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Avalonia.Skia/11.0.1": {"dependencies": {"Avalonia": "11.0.1", "HarfBuzzSharp": "*******", "HarfBuzzSharp.NativeAssets.Linux": "*******", "HarfBuzzSharp.NativeAssets.WebAssembly": "*******", "SkiaSharp": "2.88.3", "SkiaSharp.NativeAssets.Linux": "2.88.3", "SkiaSharp.NativeAssets.WebAssembly": "2.88.3"}, "runtime": {"lib/net6.0/Avalonia.Skia.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Avalonia.Themes.Fluent/11.0.1": {"dependencies": {"Avalonia": "11.0.1"}, "runtime": {"lib/net6.0/Avalonia.Themes.Fluent.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Avalonia.Themes.Simple/11.0.1": {"dependencies": {"Avalonia": "11.0.1"}, "runtime": {"lib/net6.0/Avalonia.Themes.Simple.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Avalonia.Win32/11.0.1": {"dependencies": {"Avalonia": "11.0.1", "Avalonia.Angle.Windows.Natives": "2.1.0.2023020321", "System.Drawing.Common": "6.0.0", "System.Numerics.Vectors": "4.5.0"}, "runtime": {"lib/net6.0/Avalonia.Win32.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Avalonia.X11/11.0.1": {"dependencies": {"Avalonia": "11.0.1", "Avalonia.FreeDesktop": "11.0.1", "Avalonia.Skia": "11.0.1"}, "runtime": {"lib/net6.0/Avalonia.X11.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "AvaloniaEdit.TextMate/11.0.1": {"dependencies": {"Avalonia": "11.0.1", "Avalonia.AvaloniaEdit": "11.0.1", "TextMateSharp": "1.0.55", "TextMateSharp.Grammars": "1.0.55"}, "runtime": {"lib/netstandard2.0/AvaloniaEdit.TextMate.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "HarfBuzzSharp/*******": {"dependencies": {"HarfBuzzSharp.NativeAssets.Win32": "*******", "HarfBuzzSharp.NativeAssets.macOS": "*******"}, "runtime": {"lib/net6.0/HarfBuzzSharp.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "HarfBuzzSharp.NativeAssets.Linux/*******": {"dependencies": {"HarfBuzzSharp": "*******"}, "runtimeTargets": {"runtimes/linux-arm/native/libHarfBuzzSharp.so": {"rid": "linux-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-arm64/native/libHarfBuzzSharp.so": {"rid": "linux-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-musl-x64/native/libHarfBuzzSharp.so": {"rid": "linux-musl-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/libHarfBuzzSharp.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "HarfBuzzSharp.NativeAssets.macOS/*******": {"runtimeTargets": {"runtimes/osx/native/libHarfBuzzSharp.dylib": {"rid": "osx", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "HarfBuzzSharp.NativeAssets.WebAssembly/*******": {}, "HarfBuzzSharp.NativeAssets.Win32/*******": {"runtimeTargets": {"runtimes/win-arm64/native/libHarfBuzzSharp.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/libHarfBuzzSharp.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x86/native/libHarfBuzzSharp.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "IndexRange/1.0.0": {"runtime": {"lib/netstandard2.1/IndexRange.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "MicroCom.Runtime/0.11.0": {"runtime": {"lib/net5.0/MicroCom.Runtime.dll": {"assemblyVersion": "0.11.0.0", "fileVersion": "0.11.0.0"}}}, "Microsoft.CodeAnalysis.Analyzers/3.0.0": {}, "Microsoft.CodeAnalysis.Common/3.8.0": {"dependencies": {"Microsoft.CodeAnalysis.Analyzers": "3.0.0", "System.Collections.Immutable": "5.0.0", "System.Memory": "4.5.4", "System.Reflection.Metadata": "5.0.0", "System.Runtime.CompilerServices.Unsafe": "6.0.0", "System.Text.Encoding.CodePages": "4.5.1", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/netcoreapp3.1/Microsoft.CodeAnalysis.dll": {"assemblyVersion": "*******", "fileVersion": "3.800.20.56202"}}, "resources": {"lib/netcoreapp3.1/cs/Microsoft.CodeAnalysis.resources.dll": {"locale": "cs"}, "lib/netcoreapp3.1/de/Microsoft.CodeAnalysis.resources.dll": {"locale": "de"}, "lib/netcoreapp3.1/es/Microsoft.CodeAnalysis.resources.dll": {"locale": "es"}, "lib/netcoreapp3.1/fr/Microsoft.CodeAnalysis.resources.dll": {"locale": "fr"}, "lib/netcoreapp3.1/it/Microsoft.CodeAnalysis.resources.dll": {"locale": "it"}, "lib/netcoreapp3.1/ja/Microsoft.CodeAnalysis.resources.dll": {"locale": "ja"}, "lib/netcoreapp3.1/ko/Microsoft.CodeAnalysis.resources.dll": {"locale": "ko"}, "lib/netcoreapp3.1/pl/Microsoft.CodeAnalysis.resources.dll": {"locale": "pl"}, "lib/netcoreapp3.1/pt-BR/Microsoft.CodeAnalysis.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp3.1/ru/Microsoft.CodeAnalysis.resources.dll": {"locale": "ru"}, "lib/netcoreapp3.1/tr/Microsoft.CodeAnalysis.resources.dll": {"locale": "tr"}, "lib/netcoreapp3.1/zh-Hans/Microsoft.CodeAnalysis.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp3.1/zh-Hant/Microsoft.CodeAnalysis.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.CSharp/3.8.0": {"dependencies": {"Microsoft.CodeAnalysis.Common": "3.8.0"}, "runtime": {"lib/netcoreapp3.1/Microsoft.CodeAnalysis.CSharp.dll": {"assemblyVersion": "*******", "fileVersion": "3.800.20.56202"}}, "resources": {"lib/netcoreapp3.1/cs/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "cs"}, "lib/netcoreapp3.1/de/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "de"}, "lib/netcoreapp3.1/es/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "es"}, "lib/netcoreapp3.1/fr/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "fr"}, "lib/netcoreapp3.1/it/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "it"}, "lib/netcoreapp3.1/ja/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ja"}, "lib/netcoreapp3.1/ko/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ko"}, "lib/netcoreapp3.1/pl/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "pl"}, "lib/netcoreapp3.1/pt-BR/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp3.1/ru/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ru"}, "lib/netcoreapp3.1/tr/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "tr"}, "lib/netcoreapp3.1/zh-Hans/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp3.1/zh-Hant/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.CSharp.Scripting/3.8.0": {"dependencies": {"Microsoft.CSharp": "4.3.0", "Microsoft.CodeAnalysis.CSharp": "3.8.0", "Microsoft.CodeAnalysis.Common": "3.8.0", "Microsoft.CodeAnalysis.Scripting.Common": "3.8.0"}, "runtime": {"lib/netcoreapp3.1/Microsoft.CodeAnalysis.CSharp.Scripting.dll": {"assemblyVersion": "*******", "fileVersion": "3.800.20.56202"}}, "resources": {"lib/netcoreapp3.1/cs/Microsoft.CodeAnalysis.CSharp.Scripting.resources.dll": {"locale": "cs"}, "lib/netcoreapp3.1/de/Microsoft.CodeAnalysis.CSharp.Scripting.resources.dll": {"locale": "de"}, "lib/netcoreapp3.1/es/Microsoft.CodeAnalysis.CSharp.Scripting.resources.dll": {"locale": "es"}, "lib/netcoreapp3.1/fr/Microsoft.CodeAnalysis.CSharp.Scripting.resources.dll": {"locale": "fr"}, "lib/netcoreapp3.1/it/Microsoft.CodeAnalysis.CSharp.Scripting.resources.dll": {"locale": "it"}, "lib/netcoreapp3.1/ja/Microsoft.CodeAnalysis.CSharp.Scripting.resources.dll": {"locale": "ja"}, "lib/netcoreapp3.1/ko/Microsoft.CodeAnalysis.CSharp.Scripting.resources.dll": {"locale": "ko"}, "lib/netcoreapp3.1/pl/Microsoft.CodeAnalysis.CSharp.Scripting.resources.dll": {"locale": "pl"}, "lib/netcoreapp3.1/pt-BR/Microsoft.CodeAnalysis.CSharp.Scripting.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp3.1/ru/Microsoft.CodeAnalysis.CSharp.Scripting.resources.dll": {"locale": "ru"}, "lib/netcoreapp3.1/tr/Microsoft.CodeAnalysis.CSharp.Scripting.resources.dll": {"locale": "tr"}, "lib/netcoreapp3.1/zh-Hans/Microsoft.CodeAnalysis.CSharp.Scripting.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp3.1/zh-Hant/Microsoft.CodeAnalysis.CSharp.Scripting.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.Scripting.Common/3.8.0": {"dependencies": {"Microsoft.CodeAnalysis.Common": "3.8.0"}, "runtime": {"lib/netcoreapp3.1/Microsoft.CodeAnalysis.Scripting.dll": {"assemblyVersion": "*******", "fileVersion": "3.800.20.56202"}}, "resources": {"lib/netcoreapp3.1/cs/Microsoft.CodeAnalysis.Scripting.resources.dll": {"locale": "cs"}, "lib/netcoreapp3.1/de/Microsoft.CodeAnalysis.Scripting.resources.dll": {"locale": "de"}, "lib/netcoreapp3.1/es/Microsoft.CodeAnalysis.Scripting.resources.dll": {"locale": "es"}, "lib/netcoreapp3.1/fr/Microsoft.CodeAnalysis.Scripting.resources.dll": {"locale": "fr"}, "lib/netcoreapp3.1/it/Microsoft.CodeAnalysis.Scripting.resources.dll": {"locale": "it"}, "lib/netcoreapp3.1/ja/Microsoft.CodeAnalysis.Scripting.resources.dll": {"locale": "ja"}, "lib/netcoreapp3.1/ko/Microsoft.CodeAnalysis.Scripting.resources.dll": {"locale": "ko"}, "lib/netcoreapp3.1/pl/Microsoft.CodeAnalysis.Scripting.resources.dll": {"locale": "pl"}, "lib/netcoreapp3.1/pt-BR/Microsoft.CodeAnalysis.Scripting.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp3.1/ru/Microsoft.CodeAnalysis.Scripting.resources.dll": {"locale": "ru"}, "lib/netcoreapp3.1/tr/Microsoft.CodeAnalysis.Scripting.resources.dll": {"locale": "tr"}, "lib/netcoreapp3.1/zh-Hans/Microsoft.CodeAnalysis.Scripting.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp3.1/zh-Hant/Microsoft.CodeAnalysis.Scripting.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CSharp/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Dynamic.Runtime": "4.3.0", "System.Globalization": "4.3.0", "System.Linq": "4.3.0", "System.Linq.Expressions": "4.3.0", "System.ObjectModel": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Reflection.TypeExtensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Threading": "4.3.0"}}, "Microsoft.NETCore.Platforms/2.1.2": {}, "Microsoft.NETCore.Targets/1.1.0": {}, "Microsoft.Win32.SystemEvents/6.0.0": {"runtime": {"lib/net6.0/Microsoft.Win32.SystemEvents.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/Microsoft.Win32.SystemEvents.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "Mono.Cecil/0.11.4": {"runtime": {"lib/netstandard2.0/Mono.Cecil.Mdb.dll": {"assemblyVersion": "0.11.4.0", "fileVersion": "0.11.4.0"}, "lib/netstandard2.0/Mono.Cecil.Pdb.dll": {"assemblyVersion": "0.11.4.0", "fileVersion": "0.11.4.0"}, "lib/netstandard2.0/Mono.Cecil.Rocks.dll": {"assemblyVersion": "0.11.4.0", "fileVersion": "0.11.4.0"}, "lib/netstandard2.0/Mono.Cecil.dll": {"assemblyVersion": "0.11.4.0", "fileVersion": "0.11.4.0"}}}, "Newtonsoft.Json/13.0.3-beta1": {"runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"assemblyVersion": "1*******", "fileVersion": "13.0.3.27806"}}}, "Samboy063.LibCpp2IL/2022.0.7.2": {"dependencies": {"IndexRange": "1.0.0", "Samboy063.WasmDisassembler": "2022.0.2"}, "runtime": {"lib/netstandard2.0/LibCpp2IL.dll": {"assemblyVersion": "2022.0.7.0", "fileVersion": "2022.0.7.0"}}}, "Samboy063.WasmDisassembler/2022.0.2": {"runtime": {"lib/netstandard2.0/WasmDisassembler.dll": {"assemblyVersion": "2022.0.2.0", "fileVersion": "2022.0.2.0"}}}, "SixLabors.ImageSharp/3.1.4": {"runtime": {"lib/net6.0/SixLabors.ImageSharp.dll": {"assemblyVersion": "*******", "fileVersion": "3.1.4.0"}}}, "SkiaSharp/2.88.3": {"dependencies": {"SkiaSharp.NativeAssets.Win32": "2.88.3", "SkiaSharp.NativeAssets.macOS": "2.88.3"}, "runtime": {"lib/net6.0/SkiaSharp.dll": {"assemblyVersion": "2.88.0.0", "fileVersion": "2.88.3.0"}}}, "SkiaSharp.NativeAssets.Linux/2.88.3": {"dependencies": {"SkiaSharp": "2.88.3"}, "runtimeTargets": {"runtimes/linux-arm/native/libSkiaSharp.so": {"rid": "linux-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-arm64/native/libSkiaSharp.so": {"rid": "linux-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-musl-x64/native/libSkiaSharp.so": {"rid": "linux-musl-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/libSkiaSharp.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "SkiaSharp.NativeAssets.macOS/2.88.3": {"runtimeTargets": {"runtimes/osx/native/libSkiaSharp.dylib": {"rid": "osx", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "SkiaSharp.NativeAssets.WebAssembly/2.88.3": {}, "SkiaSharp.NativeAssets.Win32/2.88.3": {"runtimeTargets": {"runtimes/win-arm64/native/libSkiaSharp.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/libSkiaSharp.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x86/native/libSkiaSharp.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "System.Collections/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.1.2", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Collections.Immutable/5.0.0": {}, "System.ComponentModel.Annotations/4.5.0": {}, "System.Diagnostics.Debug/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.1.2", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Drawing.Common/6.0.0": {"dependencies": {"Microsoft.Win32.SystemEvents": "6.0.0"}, "runtime": {"lib/net6.0/System.Drawing.Common.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}, "runtimeTargets": {"runtimes/unix/lib/net6.0/System.Drawing.Common.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}, "runtimes/win/lib/net6.0/System.Drawing.Common.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Dynamic.Runtime/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Linq": "4.3.0", "System.Linq.Expressions": "4.3.0", "System.ObjectModel": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Reflection.TypeExtensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}}, "System.Globalization/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.1.2", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.IO/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.1.2", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.IO.Pipelines/6.0.0": {"runtime": {"lib/net6.0/System.IO.Pipelines.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Linq/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0"}}, "System.Linq.Expressions/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Linq": "4.3.0", "System.ObjectModel": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Emit.Lightweight": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Reflection.TypeExtensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}}, "System.Memory/4.5.4": {}, "System.Numerics.Vectors/4.5.0": {}, "System.ObjectModel/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Threading": "4.3.0"}}, "System.Reflection/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.1.2", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Emit/4.3.0": {"dependencies": {"System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Emit.ILGeneration/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Emit.Lightweight/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.1.2", "Microsoft.NETCore.Targets": "1.1.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Metadata/5.0.0": {}, "System.Reflection.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.1.2", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Reflection.TypeExtensions/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Resources.ResourceManager/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.1.2", "Microsoft.NETCore.Targets": "1.1.0", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Runtime/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.1.2", "Microsoft.NETCore.Targets": "1.1.0"}}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {}, "System.Runtime.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.1.2", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Runtime.Handles/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.1.2", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Runtime.InteropServices/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.1.2", "Microsoft.NETCore.Targets": "1.1.0", "System.Reflection": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Handles": "4.3.0"}}, "System.Text.Encoding/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.1.2", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Text.Encoding.CodePages/4.5.1": {"dependencies": {"Microsoft.NETCore.Platforms": "2.1.2", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}}, "System.Text.Encodings.Web/7.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "runtime": {"lib/net6.0/System.Text.Encodings.Web.dll": {"assemblyVersion": "7.0.0.0", "fileVersion": "7.0.22.51805"}}, "runtimeTargets": {"runtimes/browser/lib/net6.0/System.Text.Encodings.Web.dll": {"rid": "browser", "assetType": "runtime", "assemblyVersion": "7.0.0.0", "fileVersion": "7.0.22.51805"}}}, "System.Text.Json/7.0.2": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0", "System.Text.Encodings.Web": "7.0.0"}, "runtime": {"lib/net6.0/System.Text.Json.dll": {"assemblyVersion": "7.0.0.0", "fileVersion": "7.0.323.6910"}}}, "System.Threading/4.3.0": {"dependencies": {"System.Runtime": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Threading.Tasks/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.1.2", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Threading.Tasks.Extensions/4.5.4": {}, "TextMateSharp/1.0.55": {"dependencies": {"System.Text.Json": "7.0.2"}, "runtime": {"lib/netstandard2.0/TextMateSharp.dll": {"assemblyVersion": "********", "fileVersion": "********"}}, "runtimeTargets": {"runtimes/linux/native/libonigwrap.so": {"rid": "linux", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx/native/libonigwrap.dylib": {"rid": "osx", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/onigwrap-x64.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x86/native/onigwrap-x86.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "TextMateSharp.Grammars/1.0.55": {"dependencies": {"System.Text.Json": "7.0.2", "TextMateSharp": "1.0.55"}, "runtime": {"lib/netstandard2.0/TextMateSharp.Grammars.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Tmds.DBus.Protocol/0.15.0": {"dependencies": {"System.IO.Pipelines": "6.0.0"}, "runtime": {"lib/net6.0/Tmds.DBus.Protocol.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "AssetsTools.NET.Cpp2IL/*******": {"runtime": {"AssetsTools.NET.Cpp2IL.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "AssetsTools.NET/*******": {"runtime": {"AssetsTools.NET.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "AssetsTools.NET.MonoCecil/*******": {"runtime": {"AssetsTools.NET.MonoCecil.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "AssetsTools.NET.Texture/*******": {"runtime": {"AssetsTools.NET.Texture.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}}}, "libraries": {"UABEAvalonia/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "AssetRipper.TextureDecoder/1.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-LiLbCDB1UCesQeBHw45ccEAmwNh0usomSEy0wUR8Ce+U9yEp9H6Q+qtOUBWG4bD3ogESOtSbBnRkLv9gT8lqIA==", "path": "assetripper.texturedecoder/1.2.0", "hashPath": "assetripper.texturedecoder.1.2.0.nupkg.sha512"}, "Avalonia/11.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-AZj++jzKsBp02THeGZkKUEdrcNkhAO25osH3+TjacBLgz9cJ1j2yj0zAa6/+0/uRjcyX857DJyPl5wZN79LW8Q==", "path": "avalonia/11.0.1", "hashPath": "avalonia.11.0.1.nupkg.sha512"}, "Avalonia.Angle.Windows.Natives/2.1.0.2023020321": {"type": "package", "serviceable": true, "sha512": "sha512-Zlkkb8ipxrxNWVPCJgMO19fpcpYPP+bpOQ+jPtCFj8v+TzVvPdnGHuyv9IMvSHhhMfEpps4m4hjaP4FORQYVAA==", "path": "avalonia.angle.windows.natives/2.1.0.2023020321", "hashPath": "avalonia.angle.windows.natives.2.1.0.2023020321.nupkg.sha512"}, "Avalonia.AvaloniaEdit/11.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-iv2bHjIioa3iSDeMz5LkBRiGq2r0ZT9vo9xS4LDFHK/baL3323Kon2TlXFaHK7yqmslMP3wbpre37elbV0TvCQ==", "path": "avalonia.avaloniaedit/11.0.1", "hashPath": "avalonia.avaloniaedit.11.0.1.nupkg.sha512"}, "Avalonia.BuildServices/0.0.28": {"type": "package", "serviceable": true, "sha512": "sha512-MSM0H8d8PlsPOj490DrmM4qOWAWJsweUQznZPqJ92Rdy2Rp8LfQ7JUFF0b3zceO3LXpGKKi+GSUk1GNsjjkfFQ==", "path": "avalonia.buildservices/0.0.28", "hashPath": "avalonia.buildservices.0.0.28.nupkg.sha512"}, "Avalonia.Controls.ColorPicker/11.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-6vAmC/m/u8WUm2DF6zxUeAALkh5kEUwth+H58eXtSnCYaLJdItLWypufOgryijr2Qn9G7tRhiApU9OJEGLdTEw==", "path": "avalonia.controls.colorpicker/11.0.1", "hashPath": "avalonia.controls.colorpicker.11.0.1.nupkg.sha512"}, "Avalonia.Controls.DataGrid/11.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-aLvNhplbH1wFp+hTaNTd7WUR8K1UCgdMITOoXpYKVGGLd3dYpyKDnQkV/QTJ5RBAmwpDhpLa/gC1A4jDci26Rw==", "path": "avalonia.controls.datagrid/11.0.1", "hashPath": "avalonia.controls.datagrid.11.0.1.nupkg.sha512"}, "Avalonia.Desktop/11.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-DLoaa+TknojtN64h9v+ascjyCcoTa8vp6xcvQukIKl7a764PkjlXEUYtg0XuXJloE9beMrhYO6VNUiRLekl9Zg==", "path": "avalonia.desktop/11.0.1", "hashPath": "avalonia.desktop.11.0.1.nupkg.sha512"}, "Avalonia.Diagnostics/11.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-b0FCfKvnbTBhn+xovIQ9TlPYTaewjhOmI/Q7Wl7BCg7M96NDUWUgkXv8Q6pIfnTFCXaZNf7HoCj2DAinTj+6CA==", "path": "avalonia.diagnostics/11.0.1", "hashPath": "avalonia.diagnostics.11.0.1.nupkg.sha512"}, "Avalonia.FreeDesktop/11.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-idUdeYe+1KH5jpBr9ilipek7Pto+BODwnp466weXpMga79hRYqR56b7pgadn5U8XPdHpZdDf88DRSmBe7I1kyA==", "path": "avalonia.freedesktop/11.0.1", "hashPath": "avalonia.freedesktop.11.0.1.nupkg.sha512"}, "Avalonia.Native/11.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-XS3fFr5Y//iPH1u7+puMgXlYcvCsztow/WPlLh94LkDpQjQ5DvRxQ1OxNgdMQGycy7XZ9rP1IJf/KVAd8gryOA==", "path": "avalonia.native/11.0.1", "hashPath": "avalonia.native.11.0.1.nupkg.sha512"}, "Avalonia.Remote.Protocol/11.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-d240v19iP5wcUfyEbsnA26ZQUymv1m+YKeNHmS3gl7EI6+o3ZQmfxDfMigyLV+LerUt/uDw9iDlO13ohO/j7QA==", "path": "avalonia.remote.protocol/11.0.1", "hashPath": "avalonia.remote.protocol.11.0.1.nupkg.sha512"}, "Avalonia.Skia/11.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-2HsT5+aAEUgmepv+WLJO/UW/tJBshqMgMgD7c8F1aGtPtQBq00bSX7x1ZkIFpSCCWptLbsl0d6LS764H20E9Vg==", "path": "avalonia.skia/11.0.1", "hashPath": "avalonia.skia.11.0.1.nupkg.sha512"}, "Avalonia.Themes.Fluent/11.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-FiFEcVrAfZnmabNSekwon/kX4KnNqsVK7xk8XtnrevQNdDHYt1YljH+sV61LtRzu8N4N2yrd7eQfQNtI+QPONg==", "path": "avalonia.themes.fluent/11.0.1", "hashPath": "avalonia.themes.fluent.11.0.1.nupkg.sha512"}, "Avalonia.Themes.Simple/11.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-fiHywqm7KnaZxcDjrDXO5uLJAMDnHpO09GjkPaPcsWfBH+GFE3CpUvuDZFMVyTP/IBmT3cbJT8x7Y/et0L5MQw==", "path": "avalonia.themes.simple/11.0.1", "hashPath": "avalonia.themes.simple.11.0.1.nupkg.sha512"}, "Avalonia.Win32/11.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-keDwjRSlB8S0OrmRj4+LDtkgR86z/dNNY4L/RR90iv/fOhSvS5SMyaytKYjzWR12vjIQ7V9UdE/IbU3xDTL2yg==", "path": "avalonia.win32/11.0.1", "hashPath": "avalonia.win32.11.0.1.nupkg.sha512"}, "Avalonia.X11/11.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-OKMan4T1rrQBX50cloLkumtEoKRAgyRtHJWPBXoPvDfhsyoQ+85837I5mDsgGsVqrWgYas7pSO7kVpbIqCRFyQ==", "path": "avalonia.x11/11.0.1", "hashPath": "avalonia.x11.11.0.1.nupkg.sha512"}, "AvaloniaEdit.TextMate/11.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-Kq+PZnXYww68e7XSMXst+K9Gbjfma7FNXsnJR3PELiKacIdTTQDcgPJsAetQtAKFSDhvUVDoAWtHByu3UaABcw==", "path": "avaloniaedit.textmate/11.0.1", "hashPath": "avaloniaedit.textmate.11.0.1.nupkg.sha512"}, "HarfBuzzSharp/*******": {"type": "package", "serviceable": true, "sha512": "sha512-8MwXm9J4dXHuTdzPo29nHgDbt4+6P+RrPrH/qrxcERf29cpLlFbjvP3eFPwHmdUrl4KL2SHEZi2ZuQ5ndeIL1w==", "path": "harfbuzzsharp/*******", "hashPath": "harfbuzzsharp.*******.nupkg.sha512"}, "HarfBuzzSharp.NativeAssets.Linux/*******": {"type": "package", "serviceable": true, "sha512": "sha512-Qu1yJSHEN7PD3+fqfkaClnORWN5e2xJ2Xoziz/GUi/oBT1Z+Dp2oZeiONKP6NFltboSOBkvH90QuOA6YN/U1zg==", "path": "harfbuzzsharp.nativeassets.linux/*******", "hashPath": "harfbuzzsharp.nativeassets.linux.*******.nupkg.sha512"}, "HarfBuzzSharp.NativeAssets.macOS/*******": {"type": "package", "serviceable": true, "sha512": "sha512-uwz9pB3hMuxzI/bSkjVrsOJH7Wo1L+0Md5ZmEMDM/j7xDHtR9d3mfg/CfxhMIcTiUC4JgX49FZK0y2ojgu1dww==", "path": "harfbuzzsharp.nativeassets.macos/*******", "hashPath": "harfbuzzsharp.nativeassets.macos.*******.nupkg.sha512"}, "HarfBuzzSharp.NativeAssets.WebAssembly/*******": {"type": "package", "serviceable": true, "sha512": "sha512-a6t2X1GrZDt3ErjFbG+qXdxaO8EvMMUN1AVZYfayh7EACHU3yU/SG/rveKLWhT8Ln5GFLqe2r+5dsDrHK1qScw==", "path": "harfbuzzsharp.nativeassets.webassembly/*******", "hashPath": "harfbuzzsharp.nativeassets.webassembly.*******.nupkg.sha512"}, "HarfBuzzSharp.NativeAssets.Win32/*******": {"type": "package", "serviceable": true, "sha512": "sha512-Wo6QpE4+a+PFVdfIBoLkLr4wq2uC0m9TZC8FAfy4ZnLsUc10WL0Egk9EBHHhDCeokNOXDse5YtvuTYtS/rbHfg==", "path": "harfbuzzsharp.nativeassets.win32/*******", "hashPath": "harfbuzzsharp.nativeassets.win32.*******.nupkg.sha512"}, "IndexRange/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-6TgS1JLSUkpmPLfXBPaAgB39ZUix8E4soXWk2XSDcscVe84i1JKzIAtA7jHbRHSkOOrcr6YA0MpLCeq98y9mYQ==", "path": "indexrange/1.0.0", "hashPath": "indexrange.1.0.0.nupkg.sha512"}, "MicroCom.Runtime/0.11.0": {"type": "package", "serviceable": true, "sha512": "sha512-MEnrZ3UIiH40hjzMDsxrTyi8dtqB5ziv3iBeeU4bXsL/7NLSal9F1lZKpK+tfBRnUoDSdtcW3KufE4yhATOMCA==", "path": "microcom.runtime/0.11.0", "hashPath": "microcom.runtime.0.11.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.Analyzers/3.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ojG5pGAhTPmjxRGTNvuszO3H8XPZqksDwr9xLd4Ae/JBjZZdl6GuoLk7uLMf+o7yl5wO0TAqoWcEKkEWqrZE5g==", "path": "microsoft.codeanalysis.analyzers/3.0.0", "hashPath": "microsoft.codeanalysis.analyzers.3.0.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.Common/3.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-8YTZ7GpsbTdC08DITx7/kwV0k4SC6cbBAFqc13cOm5vKJZcEIAh51tNSyGSkWisMgYCr96B2wb5Zri1bsla3+g==", "path": "microsoft.codeanalysis.common/3.8.0", "hashPath": "microsoft.codeanalysis.common.3.8.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.CSharp/3.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-hKqFCUSk9TIMBDjiYMF8/ZfK9p9mzpU+slM73CaCHu4ctfkoqJGHLQhyT8wvrYsIg+ufrUWBF8hcJYmyr5rc5Q==", "path": "microsoft.codeanalysis.csharp/3.8.0", "hashPath": "microsoft.codeanalysis.csharp.3.8.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.CSharp.Scripting/3.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-+XVKzByNigzzvl7rGwpzFrkUbbekNUwdMW3EghcxmNRZd9aamNXxes3I/U0tYx1LTeHEQ5y/nzb7SiEmXBmzEA==", "path": "microsoft.codeanalysis.csharp.scripting/3.8.0", "hashPath": "microsoft.codeanalysis.csharp.scripting.3.8.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.Scripting.Common/3.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-lR8Mxg/4tnwzFyqJOD7wBoXbyDKEaMxRc0E9UWtHNGBiL1qBdYyVhXPmiOPUL44tUJeQwCOHAr554jRHGBQIcw==", "path": "microsoft.codeanalysis.scripting.common/3.8.0", "hashPath": "microsoft.codeanalysis.scripting.common.3.8.0.nupkg.sha512"}, "Microsoft.CSharp/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-P+MBhIM0YX+JqROuf7i306ZLJEjQYA9uUyRDE+OqwUI5sh41e2ZbPQV3LfAPh+29cmceE1pUffXsGfR4eMY3KA==", "path": "microsoft.csharp/4.3.0", "hashPath": "microsoft.csharp.4.3.0.nupkg.sha512"}, "Microsoft.NETCore.Platforms/2.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-mOJy3M0UN+LUG21dLGMxaWZEP6xYpQEpLuvuEQBaownaX4YuhH6NmNUlN9si+vNkAS6dwJ//N1O4DmLf2CikVg==", "path": "microsoft.netcore.platforms/2.1.2", "hashPath": "microsoft.netcore.platforms.2.1.2.nupkg.sha512"}, "Microsoft.NETCore.Targets/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-aOZA3BWfz9RXjpzt0sRJJMjAscAUm3Hoa4UWAfceV9UTYxgwZ1lZt5nO2myFf+/jetYQo4uTP7zS8sJY67BBxg==", "path": "microsoft.netcore.targets/1.1.0", "hashPath": "microsoft.netcore.targets.1.1.0.nupkg.sha512"}, "Microsoft.Win32.SystemEvents/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-hqTM5628jSsQiv+HGpiq3WKBl2c8v1KZfby2J6Pr7pEPlK9waPdgEO6b8A/+/xn/yZ9ulv8HuqK71ONy2tg67A==", "path": "microsoft.win32.systemevents/6.0.0", "hashPath": "microsoft.win32.systemevents.6.0.0.nupkg.sha512"}, "Mono.Cecil/0.11.4": {"type": "package", "serviceable": true, "sha512": "sha512-IC1h5g0NeJGHIUgzM1P82ld57knhP0IcQfrYITDPXlNpMYGUrsG5TxuaWTjaeqDNQMBDNZkB8L0rBnwsY6JHuQ==", "path": "mono.cecil/0.11.4", "hashPath": "mono.cecil.0.11.4.nupkg.sha512"}, "Newtonsoft.Json/13.0.3-beta1": {"type": "package", "serviceable": true, "sha512": "sha512-P5VfOceZ1ZuVnrz6xiEssjigMv6VLoA4BWXpFk6KJpixf0pM9Y1Nc9zJJYPyfuCmUtw/3seUa/orRNjSh9/NBg==", "path": "newtonsoft.json/13.0.3-beta1", "hashPath": "newtonsoft.json.13.0.3-beta1.nupkg.sha512"}, "Samboy063.LibCpp2IL/2022.0.7.2": {"type": "package", "serviceable": true, "sha512": "sha512-DpVeU0dQkUJCpbw0f1xKOG8vimdK2oYhrVABC7HDE3/ikGjhnMZd3i5b1joKDHZ+AiL90TWOaEXPcy24rCZ0dA==", "path": "samboy063.libcpp2il/2022.0.7.2", "hashPath": "samboy063.libcpp2il.2022.0.7.2.nupkg.sha512"}, "Samboy063.WasmDisassembler/2022.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-2uu+xBb7M3HKfBx9tAY6jiDieAf0hRnQjSmGX+7IdE6i24dYsx6TYgtrLvRx5lOuF2M8DijZ5H8yQ1pcuCs2SA==", "path": "samboy063.wasmdisassembler/2022.0.2", "hashPath": "samboy063.wasmdisassembler.2022.0.2.nupkg.sha512"}, "SixLabors.ImageSharp/3.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-lFIdxgGDA5iYkUMRFOze7BGLcdpoLFbR+a20kc1W7NepvzU7ejtxtWOg9RvgG7kb9tBoJ3ONYOK6kLil/dgF1w==", "path": "sixlabors.imagesharp/3.1.4", "hashPath": "sixlabors.imagesharp.3.1.4.nupkg.sha512"}, "SkiaSharp/2.88.3": {"type": "package", "serviceable": true, "sha512": "sha512-GG8X3EdfwyBfwjl639UIiOVOKEdeoqDgYrz0P1MUCnefXt9cofN+AK8YB/v1+5cLMr03ieWCQdDmPqnFIzSxZw==", "path": "skiasharp/2.88.3", "hashPath": "skiasharp.2.88.3.nupkg.sha512"}, "SkiaSharp.NativeAssets.Linux/2.88.3": {"type": "package", "serviceable": true, "sha512": "sha512-wz29evZVWRqN7WHfenFwQIgqtr8f5vHCutcl1XuhWrHTRZeaIBk7ngjhyHpjUMcQxtIEAdq34ZRvMQshsBYjqg==", "path": "skiasharp.nativeassets.linux/2.88.3", "hashPath": "skiasharp.nativeassets.linux.2.88.3.nupkg.sha512"}, "SkiaSharp.NativeAssets.macOS/2.88.3": {"type": "package", "serviceable": true, "sha512": "sha512-CEbWAXMGFkPV3S1snBKK7jEG3+xud/9kmSAhu0BEUKKtlMdxx+Qal0U9bntQREM9QpqP5xLWZooodi8IlV8MEg==", "path": "skiasharp.nativeassets.macos/2.88.3", "hashPath": "skiasharp.nativeassets.macos.2.88.3.nupkg.sha512"}, "SkiaSharp.NativeAssets.WebAssembly/2.88.3": {"type": "package", "serviceable": true, "sha512": "sha512-fNKLe6jFqW4rYwaCGgvr+J7heB6S92Z52zp2z7sDSIWXgkkelrhSShDgMd/WKrSYPqlOmfOYnIGW1CQBq9amfg==", "path": "skiasharp.nativeassets.webassembly/2.88.3", "hashPath": "skiasharp.nativeassets.webassembly.2.88.3.nupkg.sha512"}, "SkiaSharp.NativeAssets.Win32/2.88.3": {"type": "package", "serviceable": true, "sha512": "sha512-MU4ASL8VAbTv5vSw1PoiWjjjpjtGhWtFYuJnrN4sNHFCePb2ohQij9JhSdqLLxk7RpRtWPdV93fbA53Pt+J0yw==", "path": "skiasharp.nativeassets.win32/2.88.3", "hashPath": "skiasharp.nativeassets.win32.2.88.3.nupkg.sha512"}, "System.Collections/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3Dcj85/TBdVpL5Zr+gEEBUuFe2icOnLalmEh9hfck1PTYbbyWuZgh4fmm2ysCLTrqLQw6t3TgTyJ+VLp+Qb+Lw==", "path": "system.collections/4.3.0", "hashPath": "system.collections.4.3.0.nupkg.sha512"}, "System.Collections.Immutable/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-FXkLXiK0sVVewcso0imKQoOxjoPAj42R8HtjjbSjVPAzwDfzoyoznWxgA3c38LDbN9SJux1xXoXYAhz98j7r2g==", "path": "system.collections.immutable/5.0.0", "hashPath": "system.collections.immutable.5.0.0.nupkg.sha512"}, "System.ComponentModel.Annotations/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-UxYQ3FGUOtzJ7LfSdnYSFd7+oEv6M8NgUatatIN2HxNtDdlcvFAf+VIq4Of9cDMJEJC0aSRv/x898RYhB4Yppg==", "path": "system.componentmodel.annotations/4.5.0", "hashPath": "system.componentmodel.annotations.4.5.0.nupkg.sha512"}, "System.Diagnostics.Debug/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZUhUOdqmaG5Jk3Xdb8xi5kIyQYAA4PnTNlHx1mu9ZY3qv4ELIdKbnL/akbGaKi2RnNUWaZsAs31rvzFdewTj2g==", "path": "system.diagnostics.debug/4.3.0", "hashPath": "system.diagnostics.debug.4.3.0.nupkg.sha512"}, "System.Drawing.Common/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-NfuoKUiP2nUWwKZN6twGqXioIe1zVD0RIj2t976A+czLHr2nY454RwwXs6JU9Htc6mwqL6Dn/nEL3dpVf2jOhg==", "path": "system.drawing.common/6.0.0", "hashPath": "system.drawing.common.6.0.0.nupkg.sha512"}, "System.Dynamic.Runtime/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-SNVi1E/vfWUAs/WYKhE9+qlS6KqK0YVhnlT0HQtr8pMIA8YX3lwy3uPMownDwdYISBdmAF/2holEIldVp85Wag==", "path": "system.dynamic.runtime/4.3.0", "hashPath": "system.dynamic.runtime.4.3.0.nupkg.sha512"}, "System.Globalization/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-kYdVd2f2PAdFGblzFswE4hkNANJBKRmsfa2X5LG2AcWE1c7/4t0pYae1L8vfZ5xvE2nK/R9JprtToA61OSHWIg==", "path": "system.globalization/4.3.0", "hashPath": "system.globalization.4.3.0.nupkg.sha512"}, "System.IO/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3qjaHvxQPDpSOYICjUoTsmoq5u6QJAFRUITgeT/4gqkF1bajbSmb1kwSxEA8AHlofqgcKJcM8udgieRNhaJ5Cg==", "path": "system.io/4.3.0", "hashPath": "system.io.4.3.0.nupkg.sha512"}, "System.IO.Pipelines/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-mXX66shZ4xLlI3vNLaJ0lt8OIZdmXTvIqXRdQX5HLVGSkLhINLsVhyZuX2UdRFnOGkqnwmMUs40pIIQ7mna4+A==", "path": "system.io.pipelines/6.0.0", "hashPath": "system.io.pipelines.6.0.0.nupkg.sha512"}, "System.Linq/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5DbqIUpsDp0dFftytzuMmc0oeMdQwjcP/EWxsksIz/w1TcFRkZ3yKKz0PqiYFMmEwPSWw+qNVqD7PJ889JzHbw==", "path": "system.linq/4.3.0", "hashPath": "system.linq.4.3.0.nupkg.sha512"}, "System.Linq.Expressions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-PGKkrd2khG4CnlyJwxwwaWWiSiWFNBGlgXvJpeO0xCXrZ89ODrQ6tjEWS/kOqZ8GwEOUATtKtzp1eRgmYNfclg==", "path": "system.linq.expressions/4.3.0", "hashPath": "system.linq.expressions.4.3.0.nupkg.sha512"}, "System.Memory/4.5.4": {"type": "package", "serviceable": true, "sha512": "sha512-1MbJTHS1lZ4bS4FmsJjnuGJOu88ZzTT2rLvrhW7Ygic+pC0NWA+3hgAen0HRdsocuQXCkUTdFn9yHJJhsijDXw==", "path": "system.memory/4.5.4", "hashPath": "system.memory.4.5.4.nupkg.sha512"}, "System.Numerics.Vectors/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-QQTlPTl06J/iiDbJCiepZ4H//BVraReU4O4EoRw1U02H5TLUIT7xn3GnDp9AXPSlJUDyFs4uWjWafNX6WrAojQ==", "path": "system.numerics.vectors/4.5.0", "hashPath": "system.numerics.vectors.4.5.0.nupkg.sha512"}, "System.ObjectModel/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-bdX+80eKv9bN6K4N+d77OankKHGn6CH711a6fcOpMQu2Fckp/Ft4L/kW9WznHpyR0NRAvJutzOMHNNlBGvxQzQ==", "path": "system.objectmodel/4.3.0", "hashPath": "system.objectmodel.4.3.0.nupkg.sha512"}, "System.Reflection/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-KMiAFoW7MfJGa9nDFNcfu+FpEdiHpWgTcS2HdMpDvt9saK3y/G4GwprPyzqjFH9NTaGPQeWNHU+iDlDILj96aQ==", "path": "system.reflection/4.3.0", "hashPath": "system.reflection.4.3.0.nupkg.sha512"}, "System.Reflection.Emit/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-228FG0jLcIwTVJyz8CLFKueVqQK36ANazUManGaJHkO0icjiIypKW7YLWLIWahyIkdh5M7mV2dJepllLyA1SKg==", "path": "system.reflection.emit/4.3.0", "hashPath": "system.reflection.emit.4.3.0.nupkg.sha512"}, "System.Reflection.Emit.ILGeneration/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-59tBslAk9733NXLrUJrwNZEzbMAcu8k344OYo+wfSVygcgZ9lgBdGIzH/nrg3LYhXceynyvTc8t5/GD4Ri0/ng==", "path": "system.reflection.emit.ilgeneration/4.3.0", "hashPath": "system.reflection.emit.ilgeneration.4.3.0.nupkg.sha512"}, "System.Reflection.Emit.Lightweight/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-oadVHGSMsTmZsAF864QYN1t1QzZjIcuKU3l2S9cZOwDdDueNTrqq1yRj7koFfIGEnKpt6NjpL3rOzRhs4ryOgA==", "path": "system.reflection.emit.lightweight/4.3.0", "hashPath": "system.reflection.emit.lightweight.4.3.0.nupkg.sha512"}, "System.Reflection.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-rJkrJD3kBI5B712aRu4DpSIiHRtr6QlfZSQsb0hYHrDCZORXCFjQfoipo2LaMUHoT9i1B7j7MnfaEKWDFmFQNQ==", "path": "system.reflection.extensions/4.3.0", "hashPath": "system.reflection.extensions.4.3.0.nupkg.sha512"}, "System.Reflection.Metadata/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-5NecZgXktdGg34rh1OenY1rFNDCI8xSjFr+Z4OU4cU06AQHUdRnIIEeWENu3Wl4YowbzkymAIMvi3WyK9U53pQ==", "path": "system.reflection.metadata/5.0.0", "hashPath": "system.reflection.metadata.5.0.0.nupkg.sha512"}, "System.Reflection.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5RXItQz5As4xN2/YUDxdpsEkMhvw3e6aNveFXUn4Hl/udNTCNhnKp8lT9fnc3MhvGKh1baak5CovpuQUXHAlIA==", "path": "system.reflection.primitives/4.3.0", "hashPath": "system.reflection.primitives.4.3.0.nupkg.sha512"}, "System.Reflection.TypeExtensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-7u6ulLcZbyxB5Gq0nMkQttcdBTx57ibzw+4IOXEfR+sXYQoHvjW5LTLyNr8O22UIMrqYbchJQJnos4eooYzYJA==", "path": "system.reflection.typeextensions/4.3.0", "hashPath": "system.reflection.typeextensions.4.3.0.nupkg.sha512"}, "System.Resources.ResourceManager/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-/zrcPkkWdZmI4F92gL/TPumP98AVDu/Wxr3CSJGQQ+XN6wbRZcyfSKVoPo17ilb3iOr0cCRqJInGwNMolqhS8A==", "path": "system.resources.resourcemanager/4.3.0", "hashPath": "system.resources.resourcemanager.4.3.0.nupkg.sha512"}, "System.Runtime/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-JufQi0vPQ0xGnAczR13AUFglDyVYt4Kqnz1AZaiKZ5+GICq0/1MH/mO/eAJHt/mHW1zjKBJd7kV26SrxddAhiw==", "path": "system.runtime/4.3.0", "hashPath": "system.runtime.4.3.0.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/iUeP3tq1S0XdNNoMz5C9twLSrM/TH+qElHkXWaPvuNOt+99G75NrV0OS2EqHx5wMN7popYjpc8oTjC1y16DLg==", "path": "system.runtime.compilerservices.unsafe/6.0.0", "hashPath": "system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512"}, "System.Runtime.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-guW0uK0fn5fcJJ1tJVXYd7/1h5F+pea1r7FLSOz/f8vPEqbR2ZAknuRDvTQ8PzAilDveOxNjSfr0CHfIQfFk8g==", "path": "system.runtime.extensions/4.3.0", "hashPath": "system.runtime.extensions.4.3.0.nupkg.sha512"}, "System.Runtime.Handles/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-OKiSUN7DmTWeYb3l51A7EYaeNMnvxwE249YtZz7yooT4gOZhmTjIn48KgSsw2k2lYdLgTKNJw/ZIfSElwDRVgg==", "path": "system.runtime.handles/4.3.0", "hashPath": "system.runtime.handles.4.3.0.nupkg.sha512"}, "System.Runtime.InteropServices/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-uv1ynXqiMK8mp1GM3jDqPCFN66eJ5w5XNomaK2XD+TuCroNTLFGeZ+WCmBMcBDyTFKou3P6cR6J/QsaqDp7fGQ==", "path": "system.runtime.interopservices/4.3.0", "hashPath": "system.runtime.interopservices.4.3.0.nupkg.sha512"}, "System.Text.Encoding/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-BiIg+KWaSDOITze6jGQynxg64naAPtqGHBwDrLaCtixsa5bKiR8dpPOHA7ge3C0JJQizJE+sfkz1wV+BAKAYZw==", "path": "system.text.encoding/4.3.0", "hashPath": "system.text.encoding.4.3.0.nupkg.sha512"}, "System.Text.Encoding.CodePages/4.5.1": {"type": "package", "serviceable": true, "sha512": "sha512-4J2JQXbftjPMppIHJ7IC+VXQ9XfEagN92vZZNoG12i+zReYlim5dMoXFC1Zzg7tsnKDM7JPo5bYfFK4Jheq44w==", "path": "system.text.encoding.codepages/4.5.1", "hashPath": "system.text.encoding.codepages.4.5.1.nupkg.sha512"}, "System.Text.Encodings.Web/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-OP6umVGxc0Z0MvZQBVigj4/U31Pw72ITihDWP9WiWDm+q5aoe0GaJivsfYGq53o6dxH7DcXWiCTl7+0o2CGdmg==", "path": "system.text.encodings.web/7.0.0", "hashPath": "system.text.encodings.web.7.0.0.nupkg.sha512"}, "System.Text.Json/7.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-/LZf/JrGyilojqwpaywb+sSz8Tew7ij4K/Sk+UW8AKfAK7KRhR6mKpKtTm06cYA7bCpGTWfYksIW+mVsdxPegQ==", "path": "system.text.json/7.0.2", "hashPath": "system.text.json.7.0.2.nupkg.sha512"}, "System.Threading/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-VkUS0kOBcUf3Wwm0TSbrevDDZ6BlM+b/HRiapRFWjM5O0NS0LviG0glKmFK+hhPDd1XFeSdU1GmlLhb2CoVpIw==", "path": "system.threading/4.3.0", "hashPath": "system.threading.4.3.0.nupkg.sha512"}, "System.Threading.Tasks/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-LbSxKEdOUhVe8BezB/9uOGGppt+nZf6e1VFyw6v3DN6lqitm0OSn2uXMOdtP0M3W4iMcqcivm2J6UgqiwwnXiA==", "path": "system.threading.tasks/4.3.0", "hashPath": "system.threading.tasks.4.3.0.nupkg.sha512"}, "System.Threading.Tasks.Extensions/4.5.4": {"type": "package", "serviceable": true, "sha512": "sha512-zteT+G8xuGu6mS+mzDzYXbzS7rd3K6Fjb9RiZlYlJPam2/hU7JCBZBVEcywNuR+oZ1ncTvc/cq0faRr3P01OVg==", "path": "system.threading.tasks.extensions/4.5.4", "hashPath": "system.threading.tasks.extensions.4.5.4.nupkg.sha512"}, "TextMateSharp/1.0.55": {"type": "package", "serviceable": true, "sha512": "sha512-did5OM<PERSON>LS0JdCgFqN/c/TOHz25izIkwrPzOxU3xWqqrMu6pL0jWA0VJX8p8B7cXo1dUGG/cMO4bTpOiJ1VW+A==", "path": "textmatesharp/1.0.55", "hashPath": "textmatesharp.1.0.55.nupkg.sha512"}, "TextMateSharp.Grammars/1.0.55": {"type": "package", "serviceable": true, "sha512": "sha512-rx4Y4fPIo4mi5BEovBybED4w/NzPfa4SL2dikXwXcjSCsoxHarQ716Y9H49wCapCfap53I4znZGCcPT5yFhY4w==", "path": "textmatesharp.grammars/1.0.55", "hashPath": "textmatesharp.grammars.1.0.55.nupkg.sha512"}, "Tmds.DBus.Protocol/0.15.0": {"type": "package", "serviceable": true, "sha512": "sha512-QVo/Y39nTYcCKBqrZuwHjXdwaky0yTQPIT3qUTEEK2MZfDtZWrJ2XyZ59zH8LBgB2fL5cWaTuP2pBTpGz/GeDQ==", "path": "tmds.dbus.protocol/0.15.0", "hashPath": "tmds.dbus.protocol.0.15.0.nupkg.sha512"}, "AssetsTools.NET.Cpp2IL/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "AssetsTools.NET/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "AssetsTools.NET.MonoCecil/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "AssetsTools.NET.Texture/*******": {"type": "reference", "serviceable": false, "sha512": ""}}}